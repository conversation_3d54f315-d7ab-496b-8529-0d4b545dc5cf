

LOCAL_DIR := $(GET_LOCAL_DIR)

MODULE := $(LOCAL_DIR)

TONGSUO_DIR := opensource_libs/Tongsuo

include $(TONGSUO_DIR)/sources.mk

# Use complete source file list from sources.mk
MODULE_SRCS := \
	$(addprefix $(TONGSUO_DIR)/,$(tongsuo_crypto_sources)) \

MODULE_INCLUDES += \
	$(TONGSUO_DIR) \
	$(TONGSUO_DIR)/include \
	$(TONGSUO_DIR)/crypto \
	$(TONGSUO_DIR)/providers/common/include \
	$(TONGSUO_DIR)/providers/implementations/include \

MODULE_CFLAGS += \
	-DTONGSUO_IMPLEMENTATION \
	-DOPENSSL_NO_STDIO \
	-DOPENSSL_NO_SOCK \
	-DOPENSSL_NO_THREADS \
	-D__TEE__ \
	-DOPENSSL_NO_ASM \
	-DOPENSSL_NO_ENGINE \
	-DOPENSSL_NO_HW \
	-DOPENSSL_NO_SECURE_MEMORY \
	-DOPEN<PERSON>L_RAND_SEED_NONE \
	-U__linux \
	-DOPENSSL_SMALL_FOOTPRINT \
	-D__GNUC_PREREQ\(maj,min\)=0 \
	-D__glibc_clang_prereq\(maj,min\)=0 \
	-Wno-unused-parameter \
	-Wno-sign-compare \
	-Wno-unused-function \
	-Wno-unused-variable \
	-Wno-implicit-function-declaration \
	-Wno-incompatible-pointer-types-discards-qualifiers \

MODULE_ASFLAGS += \
	-Wno-unused-parameter \

# Symbol prefix isolation to avoid conflicts with BoringSSL
MODULE_CFLAGS += -DSYMBOL_PREFIX=TONGSUO_

# Disable features not needed in TEE environment
MODULE_CFLAGS += \
	-DOPENSSL_NO_APPS \
	-DOPENSSL_NO_ASYNC \
	-DOPENSSL_NO_AUTOLOAD_CONFIG \
	-DOPENSSL_NO_DGRAM \
	-DOPENSSL_NO_DSO \
	-DOPENSSL_NO_DTLS \
	-DOPENSSL_NO_ENGINE \
	-DOPENSSL_NO_FIPS \
	-DOPENSSL_NO_SHARED \
	-DOPENSSL_NO_SSL \
	-DOPENSSL_NO_POSIX_IO \
	-DOPENSSL_NO_SOCK \
	-DOPENSSL_NO_DGRAM \
	-DOPENSSL_NO_SCTP \
	-DOPENSSL_NO_SPEED \
	-DOPENSSL_NO_TESTS \
	-DOPENSSL_NO_UNIT_TEST \
	-DOPENSSL_NO_DYNAMIC_ENGINE \
	-DOPENSSL_NO_STATIC_ENGINE \
	-DOPENSSL_NO_AUTOERRINIT \
	-DOPENSSL_NO_ERR \
	-DOPENSSL_NO_FILENAMES \
	-DOPENSSL_NO_STDIO \
	-DOPENSSL_NO_BIO \
	-DOPENSSL_NO_SOCK \
	-DOPENSSL_NO_DGRAM \
	-DOPENSSL_CPUID_OBJ= \
	-DOPENSSL_NO_CPUID \
	-DOPENSSL_NO_INLINE_ASM \
	-DOPENSSL_NO_BLAKE2 \
	-DOPENSSL_NO_WHIRLPOOL \
	-DOPENSSL_NO_GOST \
	-DOPENSSL_NO_IDEA \
	-DOPENSSL_NO_SEED \
	-DOPENSSL_NO_CAMELLIA \
	-DOPENSSL_NO_CAST \
	-DOPENSSL_NO_BF \
	-DOPENSSL_NO_RC2 \
	-DOPENSSL_NO_RC4 \
	-DOPENSSL_NO_RC5 \
	-DOPENSSL_NO_MDC2 \
	-DOPENSSL_NO_MD4 \
	-DOPENSSL_NO_RMD160 \
	-DOPENSSL_NO_TESTS \
	-DOPENSSL_NO_TLS \
	-DOPENSSL_NO_UI_CONSOLE \

# Enable SM algorithms
MODULE_CFLAGS += \
	-DOPENSSL_ENABLE_SM2 \
	-DOPENSSL_ENABLE_SM3 \
	-DOPENSSL_ENABLE_SM4 \

# Define required directory macros for TEE environment
MODULE_CFLAGS += \
	-DOPENSSLDIR=\"/tee/tongsuo\" \
	-DENGINESDIR=\"/tee/tongsuo/engines\" \
	-DMODULESDIR=\"/tee/tongsuo/modules\" \

include make/rctee_lib.mk
